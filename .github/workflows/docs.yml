name: Documentation Check

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  check-docs:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-3.11-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root --with docs

    - name: Install project
      run: poetry install --no-interaction --with docs

    - name: Check documentation builds
      run: |
        cd docs
        poetry run make html

    - name: Check for broken links (optional)
      run: |
        cd docs
        poetry run make linkcheck || true
