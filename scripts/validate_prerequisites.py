#!/usr/bin/env python3
"""
Phase Prerequisites Validation Script
=====================================

This script validates that all prerequisites are met before starting a new phase.
Agents MUST run this script and get a PASS result before beginning any phase.

Usage:
    python scripts/validate_prerequisites.py --phase=1.2
    python scripts/validate_prerequisites.py --phase=2.1
    python scripts/validate_prerequisites.py --list-phases
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, List, Set

# Phase dependency matrix - matches PRD specifications
PHASE_DEPENDENCIES = {
    "1.1": {
        "prerequisites": [],
        "blocks": ["1.2", "1.3", "1.4", "1.5", "2.1", "2.2", "2.3", "3.1", "3.2", "4.1", "4.2", "5.1", "5.2", "6.1", "6.2", "6.3"],
        "critical_path": True,
        "description": "Basic ROI Calculator"
    },
    "1.2": {
        "prerequisites": ["1.1"],
        "blocks": ["2.2", "3.1", "4.1", "5.1", "6.1", "6.2"],
        "critical_path": True,
        "description": "User Accounts & Data Persistence"
    },
    "1.3": {
        "prerequisites": ["1.1", "1.2"],
        "blocks": ["2.1", "3.1", "5.1"],
        "critical_path": True,
        "description": "Enhanced Risk & Cost Modeling"
    },
    "1.4": {
        "prerequisites": ["1.1", "1.2", "1.3"],
        "blocks": ["6.2"],
        "critical_path": False,
        "description": "Professional Reporting & Export"
    },
    "1.5": {
        "prerequisites": ["1.1", "1.2", "1.3", "1.4"],
        "blocks": ["6.3"],
        "critical_path": False,
        "description": "User Experience Polish & Advanced Features"
    },
    "2.1": {
        "prerequisites": ["1.3"],
        "blocks": ["2.2", "2.3", "3.1", "4.1", "5.1"],
        "critical_path": True,
        "description": "FAIR Risk Calculation Engine"
    },
    "2.2": {
        "prerequisites": ["1.2", "2.1"],
        "blocks": ["2.3", "3.1", "4.1"],
        "critical_path": True,
        "description": "Asset Management System"
    },
    "2.3": {
        "prerequisites": ["2.1", "2.2"],
        "blocks": ["3.1", "4.1", "5.1"],
        "critical_path": True,
        "description": "Risk Register & Prioritization"
    },
    "3.1": {
        "prerequisites": ["2.1", "2.3"],
        "blocks": ["3.2", "4.1"],
        "critical_path": True,
        "description": "Investment Modeling Framework"
    },
    "3.2": {
        "prerequisites": ["3.1"],
        "blocks": ["4.1", "5.1"],
        "critical_path": True,
        "description": "ROSI/TCO Analysis Engine"
    },
    "4.1": {
        "prerequisites": ["2.3", "3.1"],
        "blocks": ["4.2", "5.1"],
        "critical_path": True,
        "description": "Budget Optimization Engine"
    },
    "4.2": {
        "prerequisites": ["4.1"],
        "blocks": ["5.1", "6.1"],
        "critical_path": True,
        "description": "Staffing & Sourcing Models"
    },
    "5.1": {
        "prerequisites": ["2.3", "3.2", "4.1"],
        "blocks": ["5.2", "6.2"],
        "critical_path": True,
        "description": "Cost of Inaction Simulator"
    },
    "5.2": {
        "prerequisites": ["5.1"],
        "blocks": ["6.2"],
        "critical_path": True,
        "description": "Breach Cost & Regulatory Modeling"
    },
    "6.1": {
        "prerequisites": ["4.2", "5.2"],
        "blocks": ["6.2", "6.3"],
        "critical_path": True,
        "description": "Platform Integration"
    },
    "6.2": {
        "prerequisites": ["1.4", "5.2", "6.1"],
        "blocks": ["6.3"],
        "critical_path": True,
        "description": "Executive Reporting Dashboard"
    },
    "6.3": {
        "prerequisites": ["1.5", "6.2"],
        "blocks": [],
        "critical_path": True,
        "description": "Final Polish & Advanced Features"
    }
}

def load_phase_status() -> Dict[str, bool]:
    """Load phase completion status from status file."""
    status_file = Path("phase_status.json")
    if status_file.exists():
        with open(status_file, 'r') as f:
            return json.load(f)
    return {}

def save_phase_status(status: Dict[str, bool]) -> None:
    """Save phase completion status to status file."""
    with open("phase_status.json", 'w') as f:
        json.dump(status, f, indent=2)

def validate_phase_prerequisites(phase: str) -> tuple[bool, List[str]]:
    """
    Validate that all prerequisites for a phase are complete.
    
    Returns:
        tuple: (is_valid, list_of_missing_prerequisites)
    """
    if phase not in PHASE_DEPENDENCIES:
        return False, [f"Unknown phase: {phase}"]
    
    phase_info = PHASE_DEPENDENCIES[phase]
    prerequisites = phase_info["prerequisites"]
    
    if not prerequisites:
        return True, []  # No prerequisites required
    
    phase_status = load_phase_status()
    missing_prerequisites = []
    
    for prereq in prerequisites:
        if not phase_status.get(prereq, False):
            missing_prerequisites.append(prereq)
    
    return len(missing_prerequisites) == 0, missing_prerequisites

def mark_phase_complete(phase: str) -> None:
    """Mark a phase as complete."""
    if phase not in PHASE_DEPENDENCIES:
        print(f"❌ ERROR: Unknown phase: {phase}")
        return
    
    phase_status = load_phase_status()
    phase_status[phase] = True
    save_phase_status(phase_status)
    print(f"✅ Phase {phase} marked as COMPLETE")

def list_phases() -> None:
    """List all phases with their status and dependencies."""
    phase_status = load_phase_status()
    
    print("\n📋 PHASE DEPENDENCY OVERVIEW")
    print("=" * 80)
    
    for phase, info in PHASE_DEPENDENCIES.items():
        status = "✅ COMPLETE" if phase_status.get(phase, False) else "⏳ PENDING"
        critical = "🔥 CRITICAL PATH" if info["critical_path"] else "📋 PARALLEL"
        
        print(f"\nPhase {phase}: {info['description']}")
        print(f"  Status: {status}")
        print(f"  Priority: {critical}")
        print(f"  Prerequisites: {info['prerequisites'] or 'None'}")
        print(f"  Blocks: {len(info['blocks'])} phases")

def main():
    parser = argparse.ArgumentParser(description="Validate phase prerequisites")
    parser.add_argument("--phase", help="Phase to validate (e.g., 1.2, 2.1)")
    parser.add_argument("--complete", help="Mark phase as complete (e.g., 1.1)")
    parser.add_argument("--list-phases", action="store_true", help="List all phases")
    parser.add_argument("--reset", action="store_true", help="Reset all phase status")
    
    args = parser.parse_args()
    
    if args.list_phases:
        list_phases()
        return
    
    if args.reset:
        save_phase_status({})
        print("🔄 All phase status reset")
        return
    
    if args.complete:
        mark_phase_complete(args.complete)
        return
    
    if not args.phase:
        parser.print_help()
        return
    
    # Validate prerequisites
    is_valid, missing = validate_phase_prerequisites(args.phase)
    
    phase_info = PHASE_DEPENDENCIES.get(args.phase, {})
    
    print(f"\n🔍 PREREQUISITE VALIDATION FOR PHASE {args.phase}")
    print(f"📝 Description: {phase_info.get('description', 'Unknown')}")
    print("=" * 60)
    
    if is_valid:
        print("✅ VALIDATION PASSED")
        print("🚀 All prerequisites are complete. Phase can begin.")
        
        if phase_info.get("critical_path"):
            print("🔥 WARNING: This is a CRITICAL PATH phase - delays affect entire timeline!")
        
        blocked_phases = phase_info.get("blocks", [])
        if blocked_phases:
            print(f"⚠️  This phase blocks {len(blocked_phases)} other phases: {blocked_phases}")
    else:
        print("❌ VALIDATION FAILED")
        print("🚫 Cannot start this phase. Missing prerequisites:")
        for prereq in missing:
            prereq_desc = PHASE_DEPENDENCIES.get(prereq, {}).get('description', 'Unknown')
            print(f"   - Phase {prereq}: {prereq_desc}")
        
        print("\n💡 Next steps:")
        print("   1. Complete all missing prerequisite phases")
        print("   2. Mark completed phases with: --complete <phase>")
        print("   3. Re-run validation")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
