# Development Scripts

This directory contains utility scripts for managing the phased development process.

## Prerequisites Validation Script

### Purpose
The `validate_prerequisites.py` script ensures that agents and developers cannot start a phase until all prerequisites are complete. This prevents dependency violations and maintains project integrity.

### Usage

#### Check if a phase can be started:
```bash
python scripts/validate_prerequisites.py --phase=1.2
python scripts/validate_prerequisites.py --phase=2.1
```

#### Mark a phase as complete:
```bash
python scripts/validate_prerequisites.py --complete=1.1
```

#### List all phases and their status:
```bash
python scripts/validate_prerequisites.py --list-phases
```

#### Reset all phase status (use with caution):
```bash
python scripts/validate_prerequisites.py --reset
```

### Example Output

**Successful Validation:**
```
🔍 PREREQUISITE VALIDATION FOR PHASE 1.2
📝 Description: User Accounts & Data Persistence
============================================================
✅ VALIDATION PASSED
🚀 All prerequisites are complete. Phase can begin.
🔥 WARNING: This is a CRITICAL PATH phase - delays affect entire timeline!
⚠️  This phase blocks 6 other phases: ['2.2', '3.1', '4.1', '5.1', '6.1', '6.2']
```

**Failed Validation:**
```
🔍 PREREQUISITE VALIDATION FOR PHASE 2.1
📝 Description: FAIR Risk Calculation Engine
============================================================
❌ VALIDATION FAILED
🚫 Cannot start this phase. Missing prerequisites:
   - Phase 1.3: Enhanced Risk & Cost Modeling

💡 Next steps:
   1. Complete all missing prerequisite phases
   2. Mark completed phases with: --complete <phase>
   3. Re-run validation
```

### Integration with Development Workflow

1. **Before Starting Any Phase**: Run validation script
2. **After Completing a Phase**: Mark phase as complete
3. **Daily Standups**: Check phase status and dependencies
4. **CI/CD Integration**: Automated validation in build pipeline

### Phase Status Tracking

Phase completion status is stored in `phase_status.json` in the project root. This file tracks which phases have been completed and is used by the validation script.

Example `phase_status.json`:
```json
{
  "1.1": true,
  "1.2": true,
  "1.3": false
}
```

### Critical Path Management

Phases marked as critical path (🔥) require special attention:
- Daily progress tracking
- Immediate escalation for delays
- Priority resource allocation
- Risk mitigation planning

### Dependency Violation Prevention

The script prevents common issues:
- Starting phases without prerequisites
- Unclear dependency relationships
- Missing quality gate validations
- Incomplete documentation

### Troubleshooting

**Issue**: Script reports missing prerequisites that should be complete
**Solution**: Ensure phases are marked complete with `--complete` flag

**Issue**: Phase status file corruption
**Solution**: Use `--reset` to clear status and re-mark completed phases

**Issue**: Unknown phase error
**Solution**: Check phase naming convention (e.g., "1.1", "2.3", not "Phase 1.1")
