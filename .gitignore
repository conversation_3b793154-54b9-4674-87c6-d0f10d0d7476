# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
test-results/
test-reports/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
docs/build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.production
.env.staging
.env.test
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pyright
.pyright/

# IDEs and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.emacs.d/
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Docker
.dockerignore
docker-compose.override.yml

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# Log files
logs/
*.log
log/

# Upload directories
uploads/
media/
static/
temp/
tmp/

# Backup files
*.bak
*.backup
*.old
*.orig

# Security
.secrets
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Monitoring and profiling
.prof
*.prof
.coverage.*
coverage/

# Alembic
alembic/versions/*.py
!alembic/versions/__init__.py

# Redis dump
dump.rdb

# Celery
celerybeat.pid
celerybeat-schedule

# FastAPI specific
.pytest_cache/
.ruff_cache/

# Poetry
poetry.lock

# Node.js (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Behave
reports/

# Local development
.local/
local/

# Terraform (if using for infrastructure)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# Cloud provider configs
.aws/
.gcp/
.azure/

# Nix
.nix-postgres/
.nix-redis-data/
.nix-redis.conf
.nix-postgres.log
.nix-redis.log
.poetry-cache/
result
result-*

# Direnv
.envrc.local

# Project specific
initial-seed.md
Python\ MVP\ Template\ Improvements_.pdf
