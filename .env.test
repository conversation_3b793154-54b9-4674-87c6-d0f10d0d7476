# Test Environment Configuration
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=WARNING

# Application Settings
PROJECT_NAME=project-name-test
API_VERSION=v1
API_PREFIX=/api/v1
SECRET_KEY=test-secret-key-not-for-production
ALLOWED_HOSTS=localhost,127.0.0.1,testserver

# Test Database Configuration
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5433/project_test_db
DATABASE_BACKEND=postgresql
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=project_test_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5433

# Redis Configuration for Testing
REDIS_URL=redis://localhost:6379/1
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1

# Authentication & Security (Test Values)
JWT_SECRET_KEY=test-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=5
REFRESH_TOKEN_EXPIRE_DAYS=1

# Disable External Services in Tests
SENTRY_DSN=

# Test File Storage
UPLOAD_DIR=test_uploads
MAX_FILE_SIZE=1048576  # 1MB for tests

# Test Rate Limiting (More permissive)
RATE_LIMIT_PER_MINUTE=1000

# Disable Documentation in Tests
ENABLE_DOCS=false
ENABLE_REDOC=false
