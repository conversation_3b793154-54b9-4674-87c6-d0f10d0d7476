# Quantitative Cybersecurity Decision Platform

**An integrated suite of financial modeling and reporting tools designed to empower Chief Security Officers (CSOs) and Chief Information Security Officers (CISOs) to translate complex security risks into clear financial metrics.**

🚀 **Transform abstract security concerns into concrete financial risk metrics** with automated, defensible financial models that enable CSOs to compete effectively for budget and demonstrate security's contribution to revenue enablement.

<div align="center">

![Python](https://img.shields.io/badge/python-v3.11+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.109+-00a393.svg)
![SQLAlchemy](https://img.shields.io/badge/SQLAlchemy-2.0+-red.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

[![CI](https://github.com/forkrul/cso-platform/workflows/CI/badge.svg)](https://github.com/forkrul/cso-platform/actions/workflows/ci.yml)
[![Documentation](https://github.com/forkrul/cso-platform/workflows/Documentation%20Check/badge.svg)](https://github.com/forkrul/cso-platform/actions/workflows/docs.yml)
[![codecov](https://codecov.io/gh/forkrul/cso-platform/branch/master/graph/badge.svg)](https://codecov.io/gh/forkrul/cso-platform)

[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![Pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://github.com/pre-commit/pre-commit)

[![Docker](https://img.shields.io/badge/docker-ready-blue?logo=docker)](https://www.docker.com/)
[![Nix](https://img.shields.io/badge/nix-supported-blue?logo=nixos)](https://nixos.org/)
[![Poetry](https://img.shields.io/badge/dependency-poetry-blue)](https://python-poetry.org/)

[![Security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)
[![Checked with mypy](https://www.mypy-lang.org/static/mypy_badge.svg)](https://mypy-lang.org/)
[![Pydantic v2](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/pydantic/pydantic/main/docs/badge/v2.json)](https://pydantic.dev)

[![FAIR](https://img.shields.io/badge/FAIR-compliant-green.svg)](https://www.fairinstitute.org/)
[![NIST CSF](https://img.shields.io/badge/NIST%20CSF-integrated-blue.svg)](https://www.nist.gov/cyberframework)
[![SOC2](https://img.shields.io/badge/SOC2-Type%20II-orange.svg)](https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/aicpasoc2report.html)

</div>

## 🎯 Vision

A single, integrated platform that replaces fragmented point solutions and manual spreadsheets with automated, defensible financial models that enable CSOs to compete effectively for budget, reduce personal liability through documented decision-making, and demonstrate security's contribution to revenue enablement.

## ✨ Core Modules

### 🎯 Module 1: Risk Exposure & Prioritization Calculator
- **FAIR Risk Quantification**: Full FAIR taxonomy implementation with Monte Carlo simulation
- **Asset Management**: Comprehensive asset inventory with multiple valuation methods
- **Threat Scenario Modeling**: Pre-built library of 50+ threat scenarios mapped to MITRE ATT&CK
- **Dynamic Risk Register**: Real-time ALE calculations with interactive heat maps
- **Control Effectiveness**: Mapped to NIST CSF, ISO 27001, and CIS frameworks

### 💰 Module 2: Security Investment Justification Console
- **ROSI Calculations**: Automated return on security investment modeling
- **TCO Analysis**: Comprehensive total cost of ownership with hidden cost discovery
- **Revenue Enablement**: Quantify security's contribution to sales and customer retention
- **Comparative Analysis**: Side-by-side vendor comparison with weighted scoring
- **Investment Portfolio**: Kanban board for tracking security investments

### 📊 Module 3: Dynamic Cybersecurity Budget Allocator
- **Risk-Based Allocation**: Automated optimization engine for budget distribution
- **Industry Benchmarking**: Real-time peer comparison across verticals and company sizes
- **Scenario Planning**: Multiple budget versions with impact analysis
- **Variance Tracking**: Historical budget analysis with trend identification

### 👥 Module 4: Security Team Staffing & Sourcing Modeler
- **Staffing Calculator**: Role-based headcount modeling with 24/7 coverage analysis
- **Cost Modeling**: Fully-loaded cost calculations including benefits and training
- **Outsourcing Analysis**: MSSP vs. in-house comparison with hybrid models
- **Skills Gap Analysis**: NICE Cybersecurity Workforce Framework integration

### ⚠️ Module 5: Cost of Inaction Simulator
- **Breach Cost Modeling**: Industry-specific scenarios with Ponemon/IBM integration
- **Regulatory Fine Calculator**: Multi-jurisdiction compliance with precedent database
- **Materiality Threshold**: SEC disclosure requirements with documentation generator
- **Business Impact Analysis**: Downtime costs and reputation impact scoring

### 📈 Module 6: C-Suite Reporting Dashboard
- **Executive Dashboards**: Pre-built templates for CEO, CFO, and Board presentations
- **Automated Reporting**: Scheduled reports in multiple formats (PDF, PPT, Excel)
- **NIST CSF Integration**: Automated scoring with maturity progression tracking
- **Data Storytelling**: Narrative generator with trend analysis and insights

## 🎯 Target Users

### Primary Personas

**Strategic CSO "Sarah"**
- Chief Security Officer at Fortune 1000 company
- 15+ years experience, reports to CEO
- Needs to justify $10M+ security budget to board
- Concerned about personal liability under SEC disclosure rules

**Operational CISO "Carlos"**
- CISO at mid-market financial services firm
- 10 years experience, reports to CIO
- Limited budget requires careful prioritization
- Deciding between in-house SOC vs. MSSP

**Risk-Focused VP "Victoria"**
- VP of Cybersecurity at healthcare organization
- 8 years experience, reports to CISO
- Managing 1000+ vulnerabilities with small team
- Needs to quantify risk for non-technical executives

## 🚀 Quick Start

### Prerequisites

**Option 1: Nix (Recommended)**
- [Nix package manager](https://nixos.org/download.html)
- [direnv](https://direnv.net/) (optional but recommended)

**Option 2: Manual Setup**
- Python 3.11+
- Poetry
- PostgreSQL 16+ (for risk data and calculations)
- Redis (for caching and session management)
- Docker & Docker Compose (optional)

### Installation

#### Using Nix (Recommended)

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/cso-platform.git
   cd cso-platform
   ```

2. **Enter Nix development environment**
   ```bash
   # With Nix flakes
   nix develop

   # Or with traditional nix-shell
   nix-shell

   # Or with direnv (automatic)
   direnv allow
   ```

3. **Install Python dependencies**
   ```bash
   make install
   ```

4. **Start services and run migrations**
   ```bash
   services_start  # Start PostgreSQL and Redis
   make db-upgrade
   ```

5. **Start the development server**
   ```bash
   make dev
   ```

#### Manual Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/cso-platform.git
   cd cso-platform
   ```

2. **Install dependencies**
   ```bash
   make install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run database migrations**
   ```bash
   make db-upgrade
   ```

5. **Start the development server**
   ```bash
   make dev
   ```

6. **Visit the API documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## Nix Development Environment

This project includes comprehensive Nix support for reproducible development environments:

### Nix Quick Start

```bash
# Clone project
git clone https://github.com/forkrul/8760.git
cd 8760

# Install Nix and direnv (one-time setup)
make install-nix

# Enter development environment
nix develop  # or nix-shell

# Install dependencies and start services
make install
services_start
make dev
```

**Alternative manual Nix installation:**
```bash
# Install Nix manually
sh <(curl -L https://nixos.org/nix/install) --daemon
```

### Nix Features

- **🔒 Reproducible**: Exact same environment for all developers
- **🚀 Fast Setup**: One command gets everything working
- **🗄️ Integrated Services**: PostgreSQL and Redis included
- **🛠️ Complete Toolchain**: Python, databases, docs, testing tools
- **🔄 Automatic**: Works with direnv for seamless development

### Nix Commands

```bash
# Service management (in nix-shell)
services_start     # Start PostgreSQL and Redis
services_stop      # Stop all services
services_status    # Check service status

# Or via scripts
make nix-services-start
make nix-services-stop
make nix-services-status
```

## Development

### Using Make Commands

```bash
# Install dependencies
make install

# Run development server
make dev

# Run tests
make test
make test-unit
make test-integration
make test-e2e

# Run linting and formatting
make lint
make format

# Database operations
make db-migrate msg="Add new table"
make db-upgrade
make db-downgrade

# Documentation
make docs
make docs-serve

# Docker operations
make docker-up
make docker-down
```

### Using Docker

```bash
# Start all services
docker-compose -f docker/docker-compose.yml up -d

# View logs
docker-compose -f docker/docker-compose.yml logs -f

# Stop services
docker-compose -f docker/docker-compose.yml down
```

## 📁 Project Structure

```
cso-platform/                  # Project root
├── .github/workflows/          # GitHub Actions CI/CD
├── src/cso_platform/           # Main application code
│   ├── api/                    # API routes and endpoints
│   │   ├── v1/                 # API version 1
│   │   │   ├── risk/           # Risk quantification endpoints
│   │   │   ├── investment/     # Investment justification endpoints
│   │   │   ├── budget/         # Budget allocation endpoints
│   │   │   ├── staffing/       # Staffing model endpoints
│   │   │   ├── simulation/     # Cost of inaction endpoints
│   │   │   └── reporting/      # Dashboard and reporting endpoints
│   ├── core/                   # Core configuration and utilities
│   │   ├── config.py           # Application configuration
│   │   ├── database.py         # Database connection
│   │   ├── security.py         # Authentication and authorization
│   │   └── fair_engine.py      # FAIR risk calculation engine
│   ├── models/                 # SQLAlchemy models
│   │   ├── risk.py             # Risk and threat models
│   │   ├── asset.py            # Asset inventory models
│   │   ├── investment.py       # Investment tracking models
│   │   ├── budget.py           # Budget allocation models
│   │   ├── team.py             # Staffing and team models
│   │   └── report.py           # Reporting and dashboard models
│   ├── schemas/                # Pydantic schemas
│   │   ├── risk.py             # Risk calculation schemas
│   │   ├── investment.py       # Investment analysis schemas
│   │   ├── budget.py           # Budget planning schemas
│   │   └── reporting.py        # Dashboard and report schemas
│   ├── services/               # Business logic
│   │   ├── risk_calculator.py  # FAIR risk calculations
│   │   ├── rosi_analyzer.py    # ROSI and TCO analysis
│   │   ├── budget_optimizer.py # Budget allocation optimization
│   │   ├── staffing_modeler.py # Team sizing and cost modeling
│   │   ├── breach_simulator.py # Cost of inaction simulation
│   │   └── report_generator.py # Executive reporting
│   └── utils/                  # Utility functions
│       ├── monte_carlo.py      # Monte Carlo simulation engine
│       ├── benchmarks.py       # Industry benchmark data
│       └── integrations.py     # Third-party tool integrations
├── tests/                      # Test suite
│   ├── unit/                   # Unit tests for each module
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end workflow tests
├── docs/                       # Documentation
├── docker/                     # Docker configuration
├── alembic/                    # Database migrations
├── scripts/                    # Utility scripts
└── frontend/                   # React frontend (Next.js)
    ├── src/                    # Frontend source code
    │   ├── components/         # Reusable UI components
    │   ├── pages/              # Page components
    │   ├── hooks/              # Custom React hooks
    │   └── utils/              # Frontend utilities
    └── public/                 # Static assets
```

## Testing

The template includes comprehensive testing setup:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows with BDD

```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test types
make test-unit
make test-integration
make test-e2e
```

## Database

### Migrations

```bash
# Create a new migration
make db-migrate msg="Add user table"

# Apply migrations
make db-upgrade

# Rollback migration
make db-downgrade
```

### Models

All models inherit from `BaseModel` which provides:
- Automatic timestamps (`created_at`, `updated_at`)
- Soft delete functionality (`deleted_at`, `is_deleted`)
- Standard primary key (`id`)

## API Documentation

The API documentation is automatically generated and available at:
- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`
- **OpenAPI JSON**: `/openapi.json`

## Security

- JWT-based authentication
- Password hashing with bcrypt
- CORS configuration
- Rate limiting
- Input validation with Pydantic

## Documentation

The project includes comprehensive documentation built with Sphinx:

- **Local Documentation**: `make docs` then `make docs-serve`
- **GitHub Pages**: Deploy with `make docs-deploy`
- **Live Documentation**: https://forkrul.github.io/8760/

### Documentation Workflow

```bash
# 1. Setup GitHub Pages (one-time)
make setup-github-pages

# 2. Build documentation locally
make docs

# 3. Preview documentation
make docs-serve

# 4. Deploy to GitHub Pages
make docs-deploy
```

### GitHub Pages API Management

GitHub Pages can be configured programmatically:

```bash
# Enable GitHub Pages via script
./scripts/setup-github-pages.sh

# Or manually via API
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  https://api.github.com/repos/OWNER/REPO/pages \
  -d '{"source": {"branch": "gh-pages", "path": "/"}}'
```

## Deployment

### Production Docker

```bash
# Build production image
docker build -f docker/Dockerfile -t 8760:latest .

# Run production container
docker run -p 8000:8000 8760:latest
```

### Environment Variables

Key environment variables (see `.env.example`):

- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `SECRET_KEY`: Application secret key
- `JWT_SECRET_KEY`: JWT signing key
- `ENVIRONMENT`: Environment (development/testing/production)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.
