# Direnv configuration for automatic environment loading
# Install direnv: https://direnv.net/docs/installation.html

# Use Nix flake if available, otherwise fall back to shell.nix
if has nix && [ -f flake.nix ]; then
    echo "🚀 Loading Nix flake environment..."
    use flake
elif has nix && [ -f shell.nix ]; then
    echo "🚀 Loading Nix shell environment..."
    use nix
else
    echo "⚠️  Nix not found. Please install <PERSON> or use manual setup."
fi

# Load .env file if it exists
if [ -f .env ]; then
    echo "📝 Loading .env file..."
    dotenv .env
fi

# Set up Python path
export PYTHONPATH="$PWD/src:$PYTHONPATH"

# Add scripts to PATH
export PATH="$PWD/scripts:$PATH"

# Poetry configuration
export POETRY_VENV_IN_PROJECT=1
export POETRY_CACHE_DIR="$PWD/.poetry-cache"

# Development database URLs
export DATABASE_URL="postgresql://localhost:5432/project_dev"
export TEST_DATABASE_URL="postgresql://localhost:5432/project_test"
export REDIS_URL="redis://localhost:6379/0"

echo "✅ Environment loaded! Run 'make help' for available commands."
