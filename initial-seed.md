# Python MVP Project Template

## Project Structure

```
project-name/
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── docs.yml
├── src/
│   └── project_name/
│       ├── __init__.py
│       ├── api/
│       │   ├── __init__.py
│       │   ├── v1/
│       │   │   ├── __init__.py
│       │   │   ├── endpoints/
│       │   │   ├── dependencies.py
│       │   │   └── router.py
│       │   └── middleware/
│       ├── core/
│       │   ├── __init__.py
│       │   ├── config.py
│       │   ├── security.py
│       │   ├── database.py
│       │   └── database_manager.py
│       ├── models/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   └── mixins.py
│       ├── schemas/
│       │   ├── __init__.py
│       │   └── base.py
│       ├── services/
│       │   └── __init__.py
│       └── utils/
│           └── __init__.py
├── tests/
│   ├── __init__.py
│   ├── unit/
│   ├── integration/
│   ├── e2e/
│   │   ├── features/
│   │   └── steps/
│   └── conftest.py
├── docs/
│   ├── source/
│   │   ├── conf.py
│   │   ├── index.rst
│   │   ├── api/
│   │   ├── architecture/
│   │   └── _static/
│   ├── Makefile
│   └── requirements.txt
├── alembic/
│   ├── versions/
│   ├── alembic.ini
│   ├── env.py
│   └── script.py.mako
├── scripts/
│   ├── init-db.sh
│   ├── generate-docs.sh
│   └── migrate_database.py
├── docker/
│   ├── Dockerfile
│   ├── Dockerfile.dev
│   └── docker-compose.yml
├── .env.example
├── .env.test
├── .gitignore
├── .pre-commit-config.yaml
├── Makefile
├── pyproject.toml
├── README.md
└── requirements/
    ├── base.txt
    ├── dev.txt
    └── test.txt
```

## Core Files

### pyproject.toml

```toml
[tool.poetry]
name = "project-name"
version = "0.1.0"
description = "MVP project template with best practices"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "project_name", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.109.0"
uvicorn = {extras = ["standard"], version = "^0.27.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
sqlalchemy = "^2.0.0"
alembic = "^1.13.0"
asyncpg = "^0.29.0"
aiosqlite = "^0.19.0"
redis = "^5.0.0"
httpx = "^0.26.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
email-validator = "^2.1.0"
structlog = "^24.1.0"
sentry-sdk = "^1.40.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.2.0"
black = "^24.1.0"
isort = "^5.13.0"
mypy = "^1.8.0"
pre-commit = "^3.6.0"
ipython = "^8.20.0"
rich = "^13.7.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.0"
pytest-cov = "^4.1.0"
pytest-env = "^1.1.0"
factory-boy = "^3.3.0"
faker = "^22.0.0"
httpx = "^0.26.0"
behave = "^1.2.6"
playwright = "^1.41.0"
pytest-playwright = "^0.4.0"

[tool.poetry.group.docs.dependencies]
sphinx = "^7.2.0"
sphinx-rtd-theme = "^2.0.0"
sphinx-autodoc-typehints = "^2.0.0"
sphinxcontrib-mermaid = "^0.9.0"
myst-parser = "^2.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # flake8-eradicate
    "RUF",  # Ruff-specific rules
]
ignore = [
    "D100", # Missing docstring in public module
    "D104", # Missing docstring in public package
    "D106", # Missing docstring in public nested class
]
fix = true

[tool.ruff.pydocstyle]
convention = "google"

[tool.ruff.per-file-ignores]
"tests/*" = ["D"]
"docs/*" = ["D"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
no_implicit_optional = true
plugins = ["pydantic.mypy"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/migrations/*"]

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
```

### src/project_name/models/base.py

```python
"""Base model classes with soft delete functionality.

This module provides SQLAlchemy base models with built-in soft delete
capabilities and timestamp tracking for all database entities.
"""

from datetime import datetime
from typing import Any, Optional, Type

from sqlalchemy import Boolean, Column, DateTime, Integer, String, event
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Query, Session
from sqlalchemy.sql import func

from src.project_name.core.database_manager import metadata


class Base(DeclarativeBase):
    """Base class for all models."""
    metadata = metadata


class TimestampMixin:
    """Mixin for adding timestamp fields to models.
    
    Attributes:
        created_at: Timestamp when the record was created.
        updated_at: Timestamp when the record was last updated.
    """
    
    @declared_attr
    def created_at(cls) -> Column:
        """Timestamp when the record was created."""
        return Column(
            DateTime,
            nullable=False,
            server_default=func.now(),
            doc="Timestamp when the record was created"
        )
    
    @declared_attr
    def updated_at(cls) -> Column:
        """Timestamp when the record was last updated."""
        return Column(
            DateTime,
            nullable=False,
            server_default=func.now(),
            onupdate=func.now(),
            doc="Timestamp when the record was last updated"
        )


class SoftDeleteMixin:
    """Mixin for adding soft delete functionality to models.
    
    This mixin adds a deleted_at field and provides methods for
    soft deleting and restoring records.
    
    Attributes:
        deleted_at: Timestamp when the record was soft deleted.
        is_deleted: Boolean flag indicating if the record is deleted.
    """
    
    @declared_attr
    def deleted_at(cls) -> Column:
        """Timestamp when the record was soft deleted."""
        return Column(
            DateTime,
            nullable=True,
            doc="Timestamp when the record was soft deleted"
        )
    
    @declared_attr
    def is_deleted(cls) -> Column:
        """Boolean flag indicating if the record is deleted."""
        return Column(
            Boolean,
            nullable=False,
            server_default="false",
            default=False,
            index=True,
            doc="Boolean flag indicating if the record is deleted"
        )
    
    def soft_delete(self) -> None:
        """Mark the record as deleted."""
        self.deleted_at = datetime.utcnow()
        self.is_deleted = True
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
        self.is_deleted = False
    
    @classmethod
    def filter_active(cls, query: Query) -> Query:
        """Filter query to only include non-deleted records.
        
        Args:
            query: SQLAlchemy query object.
            
        Returns:
            Query filtered to exclude soft-deleted records.
        """
        return query.filter(cls.is_deleted == False)


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    """Base model class for all database entities.
    
    This class combines timestamp tracking and soft delete functionality
    for all models that inherit from it.
    
    Attributes:
        id: Primary key for the record.
    """
    
    __abstract__ = True
    
    id = Column(
        Integer, 
        primary_key=True, 
        index=True,
        doc="Primary key for the record"
    )
    
    def __repr__(self) -> str:
        """Return a string representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Automatically filter soft-deleted records in queries
@event.listens_for(Session, "do_orm_execute")
def receive_do_orm_execute(orm_execute_state: Any) -> None:
    """Automatically filter out soft-deleted records from queries.
    
    This event listener intercepts all ORM queries and adds a filter
    to exclude soft-deleted records unless explicitly included.
    
    Args:
        orm_execute_state: SQLAlchemy ORM execution state.
    """
    if orm_execute_state.is_select:
        for opt in orm_execute_state.update_execution_options:
            if opt.get("include_deleted", False):
                return
        
        # Add soft delete filter to all queries
        orm_execute_state.options = orm_execute_state.options.union(
            {"populate_existing": True}
        )
```

### src/project_name/schemas/base.py

```python
"""Base Pydantic schemas with common functionality.

This module provides base schemas and mixins for creating
consistent API models with proper validation and documentation.
"""

from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class TimestampSchema(BaseModel):
    """Schema mixin for timestamp fields.
    
    Attributes:
        created_at: When the resource was created.
        updated_at: When the resource was last modified.
    """
    
    created_at: datetime = Field(
        ..., 
        description="Timestamp when the resource was created"
    )
    updated_at: datetime = Field(
        ..., 
        description="Timestamp when the resource was last updated"
    )


class BaseSchema(BaseModel):
    """Base schema with common configuration.
    
    This schema provides a foundation for all API models with
    consistent configuration and behavior.
    """
    
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        use_enum_values=True,
        validate_assignment=True,
        str_strip_whitespace=True,
    )


class PaginationParams(BaseModel):
    """Schema for pagination parameters.
    
    Attributes:
        page: Current page number (1-indexed).
        per_page: Number of items per page.
        order_by: Field to order results by.
        order_dir: Direction to order results (asc/desc).
    """
    
    page: int = Field(default=1, ge=1, description="Page number")
    per_page: int = Field(
        default=20, 
        ge=1, 
        le=100, 
        description="Items per page"
    )
    order_by: str = Field(
        default="id", 
        description="Field to order by"
    )
    order_dir: str = Field(
        default="asc", 
        pattern="^(asc|desc)$", 
        description="Order direction"
    )


class PaginatedResponse(BaseModel):
    """Schema for paginated API responses.
    
    Attributes:
        items: List of items for the current page.
        total: Total number of items.
        page: Current page number.
        per_page: Number of items per page.
        pages: Total number of pages.
    """
    
    items: list[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    pages: int = Field(..., description="Total number of pages")
    
    @field_validator("pages", mode="before")
    @classmethod
    def calculate_pages(cls, v: Any, values: dict[str, Any]) -> int:
        """Calculate total pages from total items and per_page."""
        if "total" in values and "per_page" in values:
            total = values["total"]
            per_page = values["per_page"]
            return (total + per_page - 1) // per_page
        return v
```

### docker/docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: ${PROJECT_NAME:-project}_app
    volumes:
      - ../src:/app/src
      - ../tests:/app/tests
      - ../docs:/app/docs
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@db:5432/${PROJECT_NAME:-project}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${PROJECT_NAME:-project}.rule=Host(`${PROJECT_NAME:-project}.localhost`)"
      - "traefik.http.services.${PROJECT_NAME:-project}.loadbalancer.server.port=8000"
      - "traefik.http.routers.${PROJECT_NAME:-project}.entrypoints=web"
    networks:
      - app-network
      - traefik-network

  db:
    image: postgres:16-alpine
    container_name: ${PROJECT_NAME:-project}_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=${PROJECT_NAME:-project}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${DB_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    container_name: ${PROJECT_NAME:-project}_redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  test-db:
    image: postgres:16-alpine
    container_name: ${PROJECT_NAME:-project}_test_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=${PROJECT_NAME:-project}_test
    ports:
      - "${TEST_DB_PORT:-5433}:5432"
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
  traefik-network:
    external: true
```

### docs/source/conf.py

```python
"""Sphinx configuration for project documentation."""

import os
import sys
from datetime import datetime

# Add project to path
sys.path.insert(0, os.path.abspath("../../src"))

# Project information
project = "Project Name"
copyright = f"{datetime.now().year}, Your Name"
author = "Your Name"
release = "0.1.0"

# Extensions
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.napoleon",
    "sphinx.ext.viewcode",
    "sphinx.ext.intersphinx",
    "sphinx_autodoc_typehints",
    "sphinxcontrib.mermaid",
    "myst_parser",
]

# Mermaid configuration
mermaid_version = "10.6.1"
mermaid_init_js = """
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#1f77b4',
        primaryTextColor: '#fff',
        primaryBorderColor: '#7C0000',
        lineColor: '#F8B229',
        secondaryColor: '#006100',
        tertiaryColor: '#fff'
    }
});
"""

# MyST configuration
myst_enable_extensions = [
    "colon_fence",
    "deflist",
    "tasklist",
]

# Autodoc configuration
autodoc_default_options = {
    "members": True,
    "member-order": "bysource",
    "special-members": "__init__",
    "undoc-members": True,
    "show-inheritance": True,
}

# Theme
html_theme = "sphinx_rtd_theme"
html_theme_options = {
    "navigation_depth": 4,
    "collapse_navigation": False,
}

# Source suffix
source_suffix = {
    ".rst": "restructuredtext",
    ".md": "markdown",
}
```

### docs/source/architecture/index.md

```markdown
# Architecture Documentation

## System Overview

```{mermaid}
graph TB
    Client[Client Application]
    LB[Load Balancer/Traefik]
    API[FastAPI Application]
    Cache[Redis Cache]
    DB[(PostgreSQL Database)]
    Queue[Task Queue]
    
    Client --> LB
    LB --> API
    API --> Cache
    API --> DB
    API --> Queue
    
    classDef primary fill:#1f77b4,stroke:#333,stroke-width:2px,color:#fff
    classDef storage fill:#ff7f0e,stroke:#333,stroke-width:2px,color:#fff
    
    class API primary
    class DB,Cache storage
```

## API Flow

```{mermaid}
sequenceDiagram
    participant C as Client
    participant A as API
    participant M as Middleware
    participant S as Service
    participant D as Database
    
    C->>A: HTTP Request
    A->>M: Process Middleware
    M->>A: Validated Request
    A->>S: Business Logic
    S->>D: Query Data
    D-->>S: Return Data
    S-->>A: Process Response
    A-->>C: HTTP Response
```

## Database Design

```{mermaid}
erDiagram
    USER ||--o{ POST : creates
    USER ||--o{ COMMENT : writes
    POST ||--o{ COMMENT : has
    
    USER {
        int id PK
        string email UK
        string username UK
        string password_hash
        boolean is_active
        datetime created_at
        datetime updated_at
        datetime deleted_at
        boolean is_deleted
    }
    
    POST {
        int id PK
        int user_id FK
        string title
        text content
        string status
        datetime created_at
        datetime updated_at
        datetime deleted_at
        boolean is_deleted
    }
    
    COMMENT {
        int id PK
        int post_id FK
        int user_id FK
        text content
        datetime created_at
        datetime updated_at
        datetime deleted_at
        boolean is_deleted
    }
```

## Deployment Architecture

```{mermaid}
graph LR
    subgraph "Docker Network"
        T[Traefik]
        subgraph "Application Stack"
            A1[App Instance 1]
            A2[App Instance 2]
            A3[App Instance 3]
        end
        R[Redis]
        P[(PostgreSQL)]
    end
    
    subgraph "External Services"
        S3[S3 Storage]
        SM[Monitoring]
    end
    
    T --> A1
    T --> A2
    T --> A3
    A1 --> R
    A2 --> R
    A3 --> R
    A1 --> P
    A2 --> P
    A3 --> P
    A1 --> S3
    A2 --> SM
```
```

### tests/conftest.py

```python
"""Pytest configuration and fixtures."""

import asyncio
from typing import AsyncGenerator, Generator

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.project_name.core.config import settings
from src.project_name.core.database import Base, get_db
from src.project_name.main import app

# Test database URL
TEST_DATABASE_URL = settings.DATABASE_URL.replace(
    f"/{settings.POSTGRES_DB}", 
    f"/{settings.POSTGRES_DB}_test"
)

# Create test engine
engine = create_async_engine(TEST_DATABASE_URL, echo=False)
TestSessionLocal = sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a new database session for a test."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestSessionLocal() as session:
        yield session
        await session.rollback()
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture(scope="function")
def override_get_db(db_session: AsyncSession) -> None:
    """Override the get_db dependency."""
    async def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db


@pytest.fixture(scope="function")
def client(override_get_db) -> TestClient:
    """Create a test client."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="function")
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
```

### tests/e2e/features/user_management.feature

```gherkin
Feature: User Management
  As a system administrator
  I want to manage users
  So that I can control access to the system

  Background:
    Given the system is running
    And the database is clean

  Scenario: Create a new user
    Given I am authenticated as an admin
    When I create a user with email "<EMAIL>"
    Then the user should be created successfully
    And the user should appear in the user list

  Scenario: Soft delete a user
    Given a user exists with email "<EMAIL>"
    And I am authenticated as an admin
    When I delete the user "<EMAIL>"
    Then the user should be marked as deleted
    And the user should not appear in the active user list
    But the user data should still exist in the database

  Scenario: Restore a deleted user
    Given a deleted user exists with email "<EMAIL>"
    And I am authenticated as an admin
    When I restore the user "<EMAIL>"
    Then the user should be marked as active
    And the user should appear in the active user list
```

### Makefile

```makefile
.PHONY: help install dev test lint format docs clean docker-up docker-down

# Variables
PROJECT_NAME := project_name
PYTHON := python3.11
POETRY := poetry

help: ## Show this help message
	@echo "Usage: make [target]"
	@echo ""
	@echo "Targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install project dependencies
	$(POETRY) install

dev: ## Run development server
	$(POETRY) run uvicorn src.$(PROJECT_NAME).main:app --reload --host 0.0.0.0 --port 8000

test: ## Run all tests
	$(POETRY) run pytest

test-unit: ## Run unit tests only
	$(POETRY) run pytest tests/unit

test-integration: ## Run integration tests only
	$(POETRY) run pytest tests/integration

test-e2e: ## Run end-to-end tests
	$(POETRY) run behave tests/e2e/features
	$(POETRY) run pytest tests/e2e --playwright

test-cov: ## Run tests with coverage
	$(POETRY) run pytest --cov=src --cov-report=html --cov-report=term

lint: ## Run linting checks
	$(POETRY) run ruff check src tests
	$(POETRY) run mypy src

format: ## Format code
	$(POETRY) run ruff format src tests
	$(POETRY) run ruff check --fix src tests

docs: ## Build documentation
	cd docs && $(POETRY) run make html

docs-serve: ## Serve documentation locally
	cd docs && $(POETRY) run python -m http.server -d build/html 8080

clean: ## Clean build artifacts
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.coverage" -delete
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf .pytest_cache
	rm -rf .mypy_cache
	rm -rf .ruff_cache
	rm -rf docs/build

docker-up: ## Start Docker services
	docker-compose -f docker/docker-compose.yml up -d

docker-down: ## Stop Docker services
	docker-compose -f docker/docker-compose.yml down

docker-logs: ## View Docker logs
	docker-compose -f docker/docker-compose.yml logs -f

db-init: ## Initialize database with Alembic
	$(POETRY) run alembic init alembic

db-migrate: ## Create new migration
	@test -n "$(msg)" || (echo "Error: msg parameter required. Usage: make db-migrate msg='your message'" && exit 1)
	$(POETRY) run alembic revision --autogenerate -m "$(msg)"

db-upgrade: ## Run database migrations
	$(POETRY) run alembic upgrade head

db-downgrade: ## Rollback database migration
	$(POETRY) run alembic downgrade -1

db-history: ## Show migration history
	$(POETRY) run alembic history

db-current: ## Show current database revision
	$(POETRY) run alembic current

db-sqlite-upgrade: ## Run migrations with SQLite (for testing)
	DATABASE_BACKEND=sqlite $(POETRY) run alembic upgrade head

db-reset: ## Reset database (drop all tables and re-run migrations)
	$(POETRY) run alembic downgrade base
	$(POETRY) run alembic upgrade head

pre-commit: ## Run pre-commit hooks
	$(POETRY) run pre-commit run --all-files
```

### .pre-commit-config.yaml

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-toml
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.2.0
    hooks:
      - id: ruff
        args: [--fix]
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--strict, --ignore-missing-imports]

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen

  - repo: https://github.com/python-poetry/poetry
    rev: 1.7.0
    hooks:
      - id: poetry-check
      - id: poetry-lock
        args: [--no-update]
```

## Best Practices for Fast Iteration with Coding Agents

### 1. **Consistent Code Structure**
- Use strict typing with Pydantic and mypy
- Follow PEP standards enforced by Ruff
- Maintain consistent docstring format (Google style)

### 2. **Comprehensive Testing Strategy**
```python
# Unit Test Example
async def test_user_creation(db_session):
    """Test user creation with proper validation."""
    user_data = {"email": "<EMAIL>", "password": "secure123"}
    user = await create_user(db_session, user_data)
    assert user.email == user_data["email"]
```

### 3. **Service URL Management**
```python
# src/project_name/core/config.py
class Settings(BaseSettings):
    """Application settings with service discovery."""
    
    # Service URLs
    SERVICE_URLS: dict[str, str] = {
        "auth": "http://auth-service:8000",
        "notification": "http://notification-service:8000",
        "payment": "http://payment-service:8000"
    }
    
    @property
    def get_service_url(self) -> Callable[[str], str]:
        """Get service URL by name."""
        def _get_url(service: str) -> str:
            if service not in self.SERVICE_URLS:
                raise ValueError(f"Unknown service: {service}")
            return self.SERVICE_URLS[service]
        return _get_url
```

### 4. **Mermaid Diagram Templates**

```markdown
# API Flow Diagram
```mermaid
graph LR
    A[Client Request] --> B{Auth Check}
    B -->|Valid| C[Process Request]
    B -->|Invalid| D[Return 401]
    C --> E{Validate Data}
    E -->|Valid| F[Business Logic]
    E -->|Invalid| G[Return 422]
    F --> H[Database Operation]
    H --> I[Return Response]
```

# State Machine Diagram
```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Published: publish
    Published --> Archived: archive
    Published --> Draft: unpublish
    Archived --> Published: restore
    Archived --> [*]: delete
```
```

### 5. **Development Workflow**

1. **TDD Cycle**:
   ```bash
   # Write test first
   make test-unit
   # Implement feature
   # Run tests again
   make test
   ```

2. **Integration Testing**:
   ```bash
   # Run with test database
   make docker-up
   make test-integration
   ```

3. **E2E Testing**:
   ```bash
   # BDD with Behave
   make test-e2e
   # UI testing with Playwright
   poetry run playwright test
   ```

### 6. **Rapid MVP Development**

- Use factory patterns for quick model creation
- Implement feature flags for A/B testing
- Use dependency injection for easy mocking
- Maintain modular service architecture

### 7. **Documentation Generation**

```bash
# Auto-generate API docs
make docs
# Serve locally
make docs-serve
```

This template provides a solid foundation for rapid MVP development while maintaining code quality and scalability.