[tool.poetry]
name = "cso-platform"
version = "0.1.0"
description = "Quantitative Cybersecurity Decision Platform - Financial modeling and reporting tools for CSOs and CISOs"
authors = ["forkrul <<EMAIL>>"]
readme = "README.md"
packages = [{include = "cso_platform", from = "src"}]
homepage = "https://github.com/forkrul/cso-platform"
repository = "https://github.com/forkrul/cso-platform"
documentation = "https://forkrul.github.io/cso-platform/"
keywords = ["cybersecurity", "risk-management", "fair", "rosi", "ciso", "security-finance"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Information Technology",
    "Topic :: Security",
    "Topic :: Office/Business :: Financial",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.109.0"
uvicorn = {extras = ["standard"], version = "^0.27.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
sqlalchemy = "^2.0.0"
alembic = "^1.13.0"
asyncpg = "^0.29.0"
aiosqlite = "^0.19.0"
redis = "^5.0.0"
httpx = "^0.26.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
email-validator = "^2.1.0"
structlog = "^24.1.0"
sentry-sdk = "^1.40.0"
pytz = "^2024.1"
# CSO Platform specific dependencies
numpy = "^1.24.0"
pandas = "^2.0.0"
scipy = "^1.10.0"
matplotlib = "^3.7.0"
plotly = "^5.17.0"
openpyxl = "^3.1.0"
python-pptx = "^0.6.21"
reportlab = "^4.0.0"
jinja2 = "^3.1.0"
celery = "^5.3.0"
# Financial and risk modeling
quantlib = {version = "^1.32", optional = true}
# Monte Carlo simulation
pymc = {version = "^5.8.0", optional = true}

[tool.poetry.group.dev.dependencies]
ruff = "^0.2.0"
black = "^24.1.0"
isort = "^5.13.0"
mypy = "^1.8.0"
pre-commit = "^3.6.0"
ipython = "^8.20.0"
rich = "^13.7.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.0"
pytest-cov = "^4.1.0"
pytest-env = "^1.1.0"
factory-boy = "^3.3.0"
faker = "^22.0.0"
httpx = "^0.26.0"
behave = "^1.2.6"
playwright = "^1.41.0"
pytest-playwright = "^0.4.0"

[tool.poetry.group.docs.dependencies]
sphinx = "^7.2.0"
sphinx-rtd-theme = "^2.0.0"
sphinx-autodoc-typehints = "^2.0.0"
sphinxcontrib-mermaid = "^0.9.0"
myst-parser = "^2.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # flake8-eradicate
    "RUF",  # Ruff-specific rules
]
ignore = [
    "D100", # Missing docstring in public module
    "D104", # Missing docstring in public package
    "D106", # Missing docstring in public nested class
]
fix = true

[tool.ruff.pydocstyle]
convention = "google"

[tool.ruff.per-file-ignores]
"tests/*" = ["D"]
"docs/*" = ["D"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
no_implicit_optional = true
plugins = ["pydantic.mypy"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/migrations/*"]

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
